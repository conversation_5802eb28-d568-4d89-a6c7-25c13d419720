#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合数据处理和算法融合运行脚本
功能：数据预处理 -> 算法执行 -> 结果融合 -> 最终决策
"""

import os
import sys
import json
import pandas as pd
import numpy as np
import subprocess
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('algorithm_runner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class IntegratedAlgorithmRunner:
    """综合算法运行器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化运行器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_default_config()
        if config_path and os.path.exists(config_path):
            self._load_config(config_path)
        
        # 创建输出目录
        self.output_dir = self.config['output']['base_dir']
        os.makedirs(self.output_dir, exist_ok=True)
        
        logger.info(f"算法运行器初始化完成，输出目录: {self.output_dir}")

        # 验证Python环境
        self._verify_python_environment()

    def _verify_python_environment(self):
        """验证Python环境配置"""
        python_executable = self.config['python']['executable']

        if not os.path.exists(python_executable):
            logger.warning(f"指定的Python路径不存在: {python_executable}")
            logger.info("将使用系统默认Python")
            self.config['python']['executable'] = 'python'
            return

        try:
            # 测试Python环境是否可用
            result = subprocess.run(
                [python_executable, '-c', 'import sys; print(sys.executable)'],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                logger.info(f"Python环境验证成功: {result.stdout.strip()}")

                # 测试关键依赖是否可用
                test_result = subprocess.run(
                    [python_executable, '-c', 'from reformer_pytorch import LSHSelfAttention; print("reformer_pytorch可用")'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if test_result.returncode == 0:
                    logger.info("关键依赖reformer_pytorch验证成功")
                else:
                    logger.warning(f"reformer_pytorch依赖验证失败: {test_result.stderr}")
            else:
                logger.error(f"Python环境验证失败: {result.stderr}")
                logger.info("将使用系统默认Python")
                self.config['python']['executable'] = 'python'

        except Exception as e:
            logger.error(f"Python环境验证异常: {e}")
            logger.info("将使用系统默认Python")
            self.config['python']['executable'] = 'python'

    def _load_default_config(self) -> Dict:
        """加载默认配置"""
        return {
            # Python环境配置
            'python': {
                'executable': r'D:\Anaconda\envs\LTS2\python.exe',  # conda环境LTS2的Python路径
                'use_conda_env': True,
                'conda_env_name': 'LTS2'
            },

            # 输入数据配置
            'input': {
                'test_data_path': 'D:\0temp\算法1\test',  # 测试数据文件路径
                'feature_columns': ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'FLOWIN', 'FLOWOUT', 'SPP', 'CSIP'],
                'time_column': 'date'
            },
            
            # 前驱信号检测配置
            'earlysignal': {
                'enabled': True,
                'output_path': 'D:/0temp/算法1/前驱信号检测/dataset2/predicate3',  # 用户测试数据存放路径
                'feature_columns': ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP', 'date'],
                'time_window_minutes': 3,
                'run_script': '前驱信号检测/run.py',
                'run_params': {
                    'task_name': 'earlysignaldet',
                    'is_training': 1,
                    'root_path': './dataset2',
                    'model_id': 'earlysignaldetection',
                    'model': 'PatchTST',
                    'data': 'Earlysignaldet',
                    'e_layers': 3,
                    'batch_size': 16,
                    'd_model': 128,
                    'd_ff': 256,
                    'top_k': 3,
                    'des': 'Exp',
                    'itr': 1,
                    'learning_rate': 0.001,
                    'train_epochs': 200,
                    'patience': 10,
                    'do_predict': True
                }
            },
            
            # 异常检测配置
            'anomaly': {
                'enabled': True,
                'output_path': r'D:\0temp\算法1\异常检测\Time-Series-Library-main-loss\Time-Series-Library-main\dataset\5052',
                'feature_columns': ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'FLOWIN', 'FLOWOUT', 'SPP', 'CSIP'],
                'run_script': '异常检测/Time-Series-Library-main-loss/Time-Series-Library-main/run.py',
                'run_params': {
                    'task_name': 'anomaly_detection',
                    'is_training': 0,  # 测试模式
                    'root_path': './dataset',
                    'data_path': '5052',
                    'model_id': 'test_data',
                    'model': 'FEDformer',
                    'data': 'anomaly_detection',
                    'features': 'M',
                    'seq_len': 96,
                    'label_len': 48,
                    'pred_len': 96,
                    'e_layers': 3,
                    'd_model': 128,
                    'd_ff': 512,
                    'batch_size': 32,
                    'des': 'Exp',
                    'itr': 1,
                    'no_label': True
                }
            },
            
            # 融合配置
            'fusion': {
                'method': 'weighted_average',  # 融合方法
                'weights': {
                    'earlysignal': 0.6,
                    'anomaly': 0.4
                },
                'thresholds': {
                    'earlysignal': 0.6,
                    'anomaly': 0.5,
                    'fusion': 0.7
                },
                'min_warning_duration': 3  # 最小预警持续时间（分钟）
            },
            
            # 输出配置
            'output': {
                'base_dir': 'algorithm_results',
                'save_intermediate': True,
                'save_final_results': True,
                'export_format': ['csv', 'json']
            }
        }
    
    def _load_config(self, config_path: str):
        """从文件加载配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
            
            # 递归更新配置
            self._update_config(self.config, user_config)
            logger.info(f"成功加载配置文件: {config_path}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
    
    def _update_config(self, base_config: Dict, update_config: Dict):
        """递归更新配置"""
        for key, value in update_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._update_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def preprocess_data(self, input_data_path: str) -> Dict[str, str]:
        """
        数据预处理：将测试数据转换为各算法所需格式
        
        Args:
            input_data_path: 输入数据文件路径
            
        Returns:
            处理后的数据文件路径字典
        """
        logger.info("开始数据预处理...")

        # 保存当前输入文件名，用于后续文件命名
        self._current_input_filename = os.path.basename(input_data_path)
        logger.info(f"当前处理文件: {self._current_input_filename}")

        # 读取原始数据
        if not os.path.exists(input_data_path):
            raise FileNotFoundError(f"输入数据文件不存在: {input_data_path}")
        
        # 根据文件扩展名选择读取方式
        file_ext = os.path.splitext(input_data_path)[1].lower()
        if file_ext == '.csv':
            data = pd.read_csv(input_data_path)
        elif file_ext == '.npy':
            data_array = np.load(input_data_path)
            # 假设最后一列是时间列，其他是特征列
            feature_cols = self.config['input']['feature_columns']
            time_col = self.config['input']['time_column']
            columns = feature_cols + [time_col]
            data = pd.DataFrame(data_array, columns=columns[:data_array.shape[1]])
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")
        
        logger.info(f"原始数据形状: {data.shape}")
        logger.info(f"数据列: {list(data.columns)}")
        
        processed_files = {}
        
        # 1. 处理前驱信号检测数据
        if self.config['earlysignal']['enabled']:
            processed_files['earlysignal'] = self._process_earlysignal_data(data)
        
        # 2. 处理异常检测数据
        if self.config['anomaly']['enabled']:
            processed_files['anomaly'] = self._process_anomaly_data(data)
        
        logger.info("数据预处理完成")
        return processed_files
    
    def _process_earlysignal_data(self, data: pd.DataFrame) -> List[str]:
        """处理前驱信号检测数据"""
        logger.info("处理前驱信号检测数据...")

        # 选择需要的特征列（去除FLOWIN, FLOWOUT）
        earlysignal_cols = self.config['earlysignal']['feature_columns']
        earlysignal_data = data[earlysignal_cols].copy()

        # 确保输出目录存在，并完全清空旧数据
        output_path = self.config['earlysignal']['output_path']
        if os.path.exists(output_path):
            # 完全清空目录中的所有文件
            import shutil
            shutil.rmtree(output_path)
            logger.info(f"已完全清空前驱信号检测数据目录: {output_path}")

        # 重新创建目录
        os.makedirs(output_path, exist_ok=True)
        logger.info(f"重新创建前驱信号检测数据目录: {output_path}")
        
        # 按时间窗口分割数据，传递原始文件名
        time_window = self.config['earlysignal']['time_window_minutes']
        original_filename = getattr(self, '_current_input_filename', 'unknown')
        processed_files = self._split_data_by_time_window(
            earlysignal_data,
            time_window,
            output_path,
            'earlysignal',
            original_filename
        )
        
        # 验证生成的文件
        actual_files = [f for f in os.listdir(output_path) if f.endswith('.csv')]
        logger.info(f"前驱信号检测数据处理完成，生成 {len(actual_files)} 个文件")
        logger.info(f"生成的文件: {actual_files[:5]}...")  # 显示前5个文件名

        return processed_files
    
    def _process_anomaly_data(self, data: pd.DataFrame) -> str:
        """处理异常检测数据"""
        logger.info("处理异常检测数据...")

        # 选择需要的特征列（保留所有12个特征列）
        anomaly_cols = self.config['anomaly']['feature_columns']
        anomaly_data = data[anomaly_cols].copy()

        # 确保输出目录存在，并完全清空旧数据
        output_path = self.config['anomaly']['output_path']
        if os.path.exists(output_path):
            # 完全清空目录中的所有文件
            import shutil
            shutil.rmtree(output_path)
            logger.info(f"已完全清空异常检测数据目录: {output_path}")

        # 重新创建目录
        os.makedirs(output_path, exist_ok=True)
        logger.info(f"重新创建异常检测数据目录: {output_path}")

        # 转换为.npy格式，去除时间列（只保留特征列）
        # 异常检测算法期望的是纯数值特征，不包含时间列
        feature_data = anomaly_data.drop(columns=['date'], errors='ignore')
        logger.info(f"异常检测特征数据形状: {feature_data.shape}")
        logger.info(f"特征列: {list(feature_data.columns)}")

        # 使用包含井名的文件名，而不是固定的test_data.npy
        original_filename = getattr(self, '_current_input_filename', 'unknown')
        well_name = os.path.splitext(original_filename)[0]  # 去除扩展名
        output_filename = f"{well_name}_test_data.npy"
        output_file = os.path.join(output_path, output_filename)
        np.save(output_file, feature_data.values)

        # 验证生成的文件
        actual_files = [f for f in os.listdir(output_path) if f.endswith('.npy')]
        logger.info(f"异常检测数据处理完成，生成 {len(actual_files)} 个文件")
        logger.info(f"保存到: {output_file}")
        logger.info(f"文件名包含井名: {well_name}")

        return output_file
    
    def _split_data_by_time_window(self, data: pd.DataFrame, window_minutes: int,
                                   output_path: str, prefix: str, original_filename: str = 'unknown') -> List[str]:
        """按时间窗口分割数据"""
        # 确保时间列是datetime类型
        time_col = self.config['input']['time_column']
        if time_col in data.columns:
            data[time_col] = pd.to_datetime(data[time_col])
            data = data.sort_values(time_col)
        
        # 按时间窗口分割
        window_size = pd.Timedelta(minutes=window_minutes)
        start_time = data[time_col].min()
        end_time = data[time_col].max()
        
        processed_files = []
        current_time = start_time
        file_index = 1
        
        while current_time < end_time:
            window_end = current_time + window_size
            window_data = data[(data[time_col] >= current_time) & (data[time_col] < window_end)]
            
            if not window_data.empty:
                # 生成文件名，包含原始文件名信息
                time_str = current_time.strftime('%Y-%m-%d_%H-%M-%S')
                # 从原始文件名提取井名
                well_name = os.path.splitext(original_filename)[0]  # 去除扩展名
                filename = f"{well_name}_{time_str}_window{file_index}.csv"
                filepath = os.path.join(output_path, filename)
                
                # 保存文件
                window_data.to_csv(filepath, index=False)
                processed_files.append(filepath)
                
                logger.debug(f"保存时间窗口数据: {filepath}, 数据量: {len(window_data)}")
            
            current_time = window_end
            file_index += 1
        
        return processed_files

    def run_algorithms(self, processed_files: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行算法

        Args:
            processed_files: 预处理后的数据文件路径

        Returns:
            算法运行结果
        """
        logger.info("开始运行算法...")

        results = {}

        # 1. 运行前驱信号检测算法
        if self.config['earlysignal']['enabled'] and 'earlysignal' in processed_files:
            results['earlysignal'] = self._run_earlysignal_algorithm(processed_files['earlysignal'])

        # 2. 运行异常检测算法
        if self.config['anomaly']['enabled'] and 'anomaly' in processed_files:
            results['anomaly'] = self._run_anomaly_algorithm(processed_files['anomaly'])

        logger.info("算法运行完成")
        return results

    def _run_earlysignal_algorithm(self, data_files: List[str]) -> Dict[str, Any]:
        """运行前驱信号检测算法"""
        logger.info("运行前驱信号检测算法...")

        # 构建运行命令
        script_path = self.config['earlysignal']['run_script']
        params = self.config['earlysignal']['run_params']

        # 使用conda环境的Python
        python_executable = self.config['python']['executable']
        cmd = [python_executable, script_path]
        for key, value in params.items():
            if key == 'do_predict':
                if value:
                    cmd.append('--do_predict')
            else:
                cmd.extend([f'--{key}', str(value)])

        try:
            # 切换到前驱信号检测目录
            original_cwd = os.getcwd()
            earlysignal_dir = os.path.dirname(script_path)
            if earlysignal_dir:
                os.chdir(earlysignal_dir)
                cmd[1] = os.path.basename(script_path)  # 使用相对路径

            logger.info(f"执行命令: {' '.join(cmd)}")
            logger.info(f"使用Python环境: {python_executable}")

            # 运行算法
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600  # 1小时超时
            )

            # 恢复原始工作目录
            os.chdir(original_cwd)

            if result.returncode == 0:
                logger.info("前驱信号检测算法运行成功")
                return {
                    'status': 'success',
                    'output': result.stdout,
                    'predictions': self._parse_earlysignal_results()
                }
            else:
                logger.error(f"前驱信号检测算法运行失败: {result.stderr}")
                return {
                    'status': 'failed',
                    'error': result.stderr
                }

        except subprocess.TimeoutExpired:
            logger.error("前驱信号检测算法运行超时")
            return {'status': 'timeout'}
        except Exception as e:
            logger.error(f"前驱信号检测算法运行异常: {e}")
            return {'status': 'error', 'error': str(e)}

    def _run_anomaly_algorithm(self, data_file: str) -> Dict[str, Any]:
        """运行异常检测算法"""
        logger.info("运行异常检测算法...")

        # 不检查现有结果，强制重新运行算法以确保使用最新数据
        logger.info("强制运行异常检测算法以确保使用最新数据")

        # 检查是否应该使用batch_test.py而不是run.py
        # 因为run.py中的data_loader.py有固定的文件名逻辑，而batch_test.py更灵活
        script_path = self.config['anomaly']['run_script']
        anomaly_dir = os.path.dirname(script_path)
        batch_test_script = os.path.join(anomaly_dir, 'batch_test.py')

        test_filename = os.path.basename(data_file)
        logger.info(f"测试文件名: {test_filename}")

        if os.path.exists(batch_test_script):
            logger.info("使用batch_test.py运行异常检测，以支持动态文件名")
            script_to_use = batch_test_script
            use_batch_test = True
        else:
            logger.info("使用run.py运行异常检测")
            script_to_use = script_path
            use_batch_test = False

        # 设置参数
        if not use_batch_test:
            params = self.config['anomaly']['run_params'].copy()
            params['test_file'] = test_filename
            logger.info(f"设置测试文件参数: test_file={test_filename}")
        else:
            # batch_test.py通常不需要命令行参数，它会自动处理目录中的文件
            params = {}

        try:
            # 切换到异常检测目录
            original_cwd = os.getcwd()
            if anomaly_dir:
                os.chdir(anomaly_dir)
                script_name = os.path.basename(script_to_use)

                # 清空test_results目录，确保使用最新结果
                test_results_dir = os.path.join(anomaly_dir, "test_results")
                if os.path.exists(test_results_dir):
                    import shutil
                    shutil.rmtree(test_results_dir)
                    logger.info(f"已清空异常检测结果目录: {test_results_dir}")

            else:
                script_name = script_to_use

            logger.info(f"执行异常检测脚本: {script_name}")

            # 使用conda环境的Python
            python_executable = self.config['python']['executable']
            logger.info(f"使用Python环境: {python_executable}")

            # 构建运行命令
            cmd = [python_executable, script_name]

            # 只有在使用run.py时才添加参数
            if not use_batch_test:
                for key, value in params.items():
                    if key == 'no_label':
                        if value:
                            cmd.append('--no_label')
                    else:
                        cmd.extend([f'--{key}', str(value)])
            else:
                logger.info("使用batch_test.py，无需额外参数")

            logger.info(f"执行命令: {' '.join(cmd)}")

            # 运行算法
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600  # 1小时超时
            )

            # 恢复原始工作目录
            os.chdir(original_cwd)

            if result.returncode == 0:
                logger.info("异常检测算法运行成功")
                return {
                    'status': 'success',
                    'output': result.stdout,
                    'predictions': self._parse_anomaly_results()
                }
            else:
                logger.error(f"异常检测算法运行失败: {result.stderr}")
                return {
                    'status': 'failed',
                    'error': result.stderr
                }

        except subprocess.TimeoutExpired:
            logger.error("异常检测算法运行超时")
            return {'status': 'timeout'}
        except Exception as e:
            logger.error(f"异常检测算法运行异常: {e}")
            return {'status': 'error', 'error': str(e)}

    def _parse_earlysignal_results(self) -> List[Dict]:
        """解析前驱信号检测结果"""
        logger.info("解析前驱信号检测结果...")

        # 前驱信号检测结果文件路径
        result_file = "前驱信号检测/results/earlysignaldetection_PatchTST_Earlysignaldet_ftM_sl96_ll48_pl96_dm128_nh8_el3_dl1_df256_fc5_ebtimeF_dtTrue_Exp_0/pred.npy"

        # 如果npy文件不存在，尝试查找CSV文件
        if not os.path.exists(result_file):
            # 查找可能的结果文件
            import glob
            possible_files = [
                "前驱信号检测/predictions.csv",
                "前驱信号检测/results/**/pred.npy",
                "前驱信号检测/results/**/predictions.csv"
            ]

            for pattern in possible_files:
                files = glob.glob(pattern, recursive=True)
                if files:
                    result_file = files[0]
                    break

        if not os.path.exists(result_file):
            logger.warning(f"前驱信号检测结果文件不存在: {result_file}")
            return []

        try:
            if result_file.endswith('.npy'):
                # 处理NPY文件
                predictions = np.load(result_file)
                logger.info(f"读取前驱信号检测结果，共 {len(predictions)} 条记录")

                # 获取对应的测试文件列表
                test_files = []
                data_path = self.config['earlysignal']['output_path']
                if os.path.exists(data_path):
                    test_files = [f for f in os.listdir(data_path) if f.endswith('.csv')]

                results = []
                for i, pred in enumerate(predictions):
                    filename = test_files[i] if i < len(test_files) else f"test_{i}.csv"
                    # 假设预测值大于0.5表示有风险
                    risk_score = float(pred[0]) if len(pred) > 0 else 0.0
                    predicted_label = 1 if risk_score > 0.5 else 0

                    results.append({
                        'filename': filename,
                        'risk_score': risk_score,
                        'predicted_label': predicted_label,
                        'algorithm': 'earlysignal'
                    })

                return results

            else:
                # 处理CSV文件，尝试多种编码格式
                df = None
                for encoding in ['gbk', 'gb2312', 'utf-8', 'latin1']:
                    try:
                        df = pd.read_csv(result_file, encoding=encoding)
                        logger.info(f"成功读取前驱信号检测结果，使用编码: {encoding}，共 {len(df)} 条记录")
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        logger.warning(f"使用编码 {encoding} 读取失败: {e}")
                        continue

                if df is None:
                    logger.error(f"无法读取前驱信号检测结果文件，所有编码尝试均失败: {result_file}")
                    return []

                # 转换为标准格式
                results = []
                for _, row in df.iterrows():
                    results.append({
                        'filename': row['Filename'],
                        'risk_score': float(row['Risk']),
                        'predicted_label': int(row['Predicted_Label']),
                        'algorithm': 'earlysignal'
                    })

                return results

        except Exception as e:
            logger.error(f"解析前驱信号检测结果失败: {e}")
            return []

    def _parse_anomaly_results(self) -> List[Dict]:
        """解析异常检测结果"""
        logger.info("解析异常检测结果...")

        # 异常检测结果目录（只使用当前运行生成的结果）
        result_dir = "异常检测/Time-Series-Library-main-loss/Time-Series-Library-main/test_results"

        # 严格验证当前运行的结果
        if not os.path.exists(result_dir):
            logger.error(f"异常检测结果目录不存在: {result_dir}")
            logger.error("这表明异常检测算法运行失败或配置错误")
            raise FileNotFoundError(f"异常检测结果目录不存在: {result_dir}")

        import glob
        pattern = os.path.join(result_dir, "**/anomaly_results_*.csv")
        current_results = glob.glob(pattern, recursive=True)

        if not current_results:
            logger.error(f"异常检测结果目录存在但没有结果文件: {result_dir}")
            logger.error("检查异常检测算法是否正确运行并生成了结果文件")
            logger.error("预期文件格式: anomaly_results_*.csv")

            # 列出目录中的实际文件，帮助诊断
            try:
                actual_files = os.listdir(result_dir)
                logger.error(f"目录中实际存在的文件: {actual_files}")
            except Exception as e:
                logger.error(f"无法列出目录内容: {e}")

            raise FileNotFoundError(f"异常检测结果文件不存在，算法可能运行失败")

        logger.info(f"找到 {len(current_results)} 个异常检测结果文件")
        logger.info(f"使用结果目录: {result_dir}")

        results = []

        try:
            # 查找所有异常检测结果文件
            import glob
            pattern = os.path.join(result_dir, "**/anomaly_results_*.csv")
            result_files = glob.glob(pattern, recursive=True)

            logger.info(f"找到 {len(result_files)} 个异常检测结果文件")

            for result_file in result_files:
                try:
                    # 从文件名提取测试文件信息
                    filename = os.path.basename(result_file)
                    # 提取测试文件名，支持包含井名的文件名格式
                    if 'anomaly_results_' in filename:
                        # 处理格式：anomaly_results_井名_test_data_时间戳.csv
                        test_filename = filename.replace('anomaly_results_', '').split('_202')[0]
                        # 如果包含_test_data，则去除这部分
                        if '_test_data' in test_filename:
                            test_filename = test_filename.replace('_test_data', '')
                    else:
                        # 直接使用文件名（去除扩展名）
                        test_filename = os.path.splitext(filename)[0]

                    # 读取结果文件
                    df = pd.read_csv(result_file)

                    # 处理布尔值转换为整数
                    if df['Predicted_Anomaly'].dtype == 'bool':
                        df['Predicted_Anomaly'] = df['Predicted_Anomaly'].astype(int)
                    elif df['Predicted_Anomaly'].dtype == 'object':
                        # 处理字符串形式的布尔值
                        df['Predicted_Anomaly'] = df['Predicted_Anomaly'].map({'True': 1, 'False': 0, True: 1, False: 0})

                    # 计算异常统计
                    total_points = len(df)
                    anomaly_points = df['Predicted_Anomaly'].sum()
                    anomaly_ratio = anomaly_points / total_points if total_points > 0 else 0
                    avg_anomaly_score = df['Anomaly_Score'].mean()

                    # 提取异常时间段
                    anomaly_periods = self._extract_anomaly_periods_from_data(df)

                    results.append({
                        'filename': test_filename,
                        'total_points': total_points,
                        'anomaly_points': int(anomaly_points),
                        'anomaly_ratio': anomaly_ratio,
                        'avg_anomaly_score': float(avg_anomaly_score),
                        'predicted_label': 1 if anomaly_ratio > 0.1 else 0,  # 阈值可配置
                        'algorithm': 'anomaly',
                        'anomaly_periods': anomaly_periods,  # 异常时间段
                        'detail_data': df.to_dict('records')  # 保存详细数据
                    })

                except Exception as e:
                    logger.error(f"解析异常检测结果文件失败 {result_file}: {e}")
                    continue

            logger.info(f"成功解析 {len(results)} 个异常检测结果")
            return results

        except Exception as e:
            logger.error(f"解析异常检测结果失败: {e}")
            return []

    def _extract_anomaly_periods_from_data(self, df: pd.DataFrame) -> List[Dict]:
        """从异常检测数据中提取异常时间段"""
        try:
            # 确保时间戳列是datetime类型
            df['Timestamp'] = pd.to_datetime(df['Timestamp'])

            # 找出所有异常点
            anomaly_points = df[df['Predicted_Anomaly'] == True].copy()

            if anomaly_points.empty:
                return []

            # 按时间排序
            anomaly_points = anomaly_points.sort_values('Timestamp')

            # 合并连续的异常时间段
            periods = []
            current_start = None
            current_end = None

            for _, row in anomaly_points.iterrows():
                timestamp = row['Timestamp']

                if current_start is None:
                    # 开始新的时间段
                    current_start = timestamp
                    current_end = timestamp
                else:
                    # 检查是否与前一个时间点连续（间隔小于5分钟）
                    time_diff = (timestamp - current_end).total_seconds() / 60
                    if time_diff <= 5:  # 5分钟内认为是连续的
                        current_end = timestamp
                    else:
                        # 保存当前时间段，开始新的时间段
                        if current_start and current_end:
                            duration = (current_end - current_start).total_seconds() / 60
                            if duration >= 1:  # 至少持续1分钟才算有效预警
                                periods.append({
                                    'start_time': current_start.strftime('%Y-%m-%d %H:%M:%S'),
                                    'end_time': current_end.strftime('%Y-%m-%d %H:%M:%S'),
                                    'duration_minutes': int(duration)
                                })
                        current_start = timestamp
                        current_end = timestamp

            # 处理最后一个时间段
            if current_start and current_end:
                duration = (current_end - current_start).total_seconds() / 60
                if duration >= 1:  # 至少持续1分钟才算有效预警
                    periods.append({
                        'start_time': current_start.strftime('%Y-%m-%d %H:%M:%S'),
                        'end_time': current_end.strftime('%Y-%m-%d %H:%M:%S'),
                        'duration_minutes': int(duration)
                    })

            return periods

        except Exception as e:
            logger.error(f"提取异常时间段失败: {e}")
            return []

    def fusion_results(self, algorithm_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        融合算法结果

        Args:
            algorithm_results: 各算法的运行结果

        Returns:
            融合后的结果
        """
        logger.info("开始结果融合...")

        fusion_config = self.config['fusion']
        fusion_method = fusion_config['method']

        # 提取有效的预测结果
        valid_predictions = {}
        for algo_name, result in algorithm_results.items():
            if result.get('status') == 'success' and result.get('predictions'):
                valid_predictions[algo_name] = result['predictions']

        if not valid_predictions:
            logger.warning("没有有效的算法预测结果可供融合")
            return {'status': 'no_valid_predictions', 'warning_periods': []}

        # 根据融合方法进行融合
        if fusion_method == 'weighted_average':
            fused_result = self._weighted_average_fusion(valid_predictions, fusion_config)
        elif fusion_method == 'majority_voting':
            fused_result = self._majority_voting_fusion(valid_predictions, fusion_config)
        else:
            logger.error(f"不支持的融合方法: {fusion_method}")
            return {'status': 'unsupported_method'}

        # 识别预警时间段
        warning_periods = self._identify_warning_periods(fused_result, fusion_config)

        # 统计实际的预警时间段数量
        total_warnings = 0
        for period in warning_periods:
            if period.get('earlysignal_warnings'):
                total_warnings += len(period['earlysignal_warnings'].split(';')) if period['earlysignal_warnings'] else 0
            if period.get('anomaly_warnings'):
                total_warnings += len(period['anomaly_warnings'].split(';')) if period['anomaly_warnings'] else 0
            if period.get('fusion_warnings'):
                total_warnings += len(period['fusion_warnings'].split(';')) if period['fusion_warnings'] else 0

        logger.info(f"结果融合完成，处理了 {len(warning_periods)} 口井，识别出 {total_warnings} 个预警时间段")

        return {
            'status': 'success',
            'fusion_method': fusion_method,
            'fused_predictions': fused_result,
            'warning_periods': warning_periods,
            'algorithm_results': algorithm_results
        }

    def _weighted_average_fusion(self, predictions: Dict[str, List], config: Dict) -> List[Dict]:
        """加权平均融合"""
        logger.info("执行加权平均融合...")

        weights = config['weights']
        fused_predictions = []

        # 获取所有测试文件名
        all_filenames = set()
        for algo_predictions in predictions.values():
            for pred in algo_predictions:
                all_filenames.add(pred['filename'])

        # 对每个文件进行融合
        for filename in all_filenames:
            # 收集该文件的所有算法预测
            file_predictions = {}
            for algo_name, algo_predictions in predictions.items():
                for pred in algo_predictions:
                    if pred['filename'] == filename:
                        file_predictions[algo_name] = pred
                        break

            # 计算加权融合分数
            weighted_score = 0.0
            total_weight = 0.0

            for algo_name, weight in weights.items():
                if algo_name in file_predictions:
                    if algo_name == 'earlysignal':
                        score = file_predictions[algo_name]['risk_score']
                    elif algo_name == 'anomaly':
                        score = file_predictions[algo_name]['anomaly_ratio']
                    else:
                        continue

                    weighted_score += score * weight
                    total_weight += weight

            # 归一化分数
            if total_weight > 0:
                final_score = weighted_score / total_weight
            else:
                final_score = 0.0

            # 根据阈值确定预测标签
            fusion_threshold = config['thresholds']['fusion']
            predicted_label = 1 if final_score > fusion_threshold else 0

            fused_predictions.append({
                'filename': filename,
                'fusion_score': final_score,
                'predicted_label': predicted_label,
                'algorithm_predictions': file_predictions
            })

        logger.info(f"加权平均融合完成，处理了 {len(fused_predictions)} 个文件")
        return fused_predictions

    def _majority_voting_fusion(self, predictions: Dict[str, List], config: Dict) -> List[Dict]:
        """多数投票融合"""
        logger.info("执行多数投票融合...")

        fused_predictions = []

        # 获取所有测试文件名
        all_filenames = set()
        for algo_predictions in predictions.values():
            for pred in algo_predictions:
                all_filenames.add(pred['filename'])

        # 对每个文件进行投票融合
        for filename in all_filenames:
            # 收集该文件的所有算法预测
            file_predictions = {}
            votes = 0
            total_algorithms = 0

            for algo_name, algo_predictions in predictions.items():
                for pred in algo_predictions:
                    if pred['filename'] == filename:
                        file_predictions[algo_name] = pred
                        if pred['predicted_label'] == 1:
                            votes += 1
                        total_algorithms += 1
                        break

            # 多数投票决策
            predicted_label = 1 if votes > total_algorithms / 2 else 0
            confidence = votes / total_algorithms if total_algorithms > 0 else 0

            fused_predictions.append({
                'filename': filename,
                'votes': votes,
                'total_algorithms': total_algorithms,
                'confidence': confidence,
                'predicted_label': predicted_label,
                'algorithm_predictions': file_predictions
            })

        logger.info(f"多数投票融合完成，处理了 {len(fused_predictions)} 个文件")
        return fused_predictions

    def _identify_warning_periods(self, fused_predictions: List[Dict], config: Dict) -> List[Dict]:
        """识别预警时间段"""
        logger.info("识别预警时间段...")

        warning_periods = []

        # 按井名分组处理
        well_groups = {}
        for pred in fused_predictions:
            filename = pred['filename']
            # 从文件名提取井名，处理编码问题
            well_name = self._extract_well_name_from_filename(filename)

            if well_name not in well_groups:
                well_groups[well_name] = []
            well_groups[well_name].append(pred)

        # 为每口井生成预警时间段
        for well_name, predictions in well_groups.items():
            # 排序预测结果（按文件名中的时间）
            predictions.sort(key=lambda x: x['filename'])

            # 提取各算法的预警时间段
            earlysignal_periods = []
            anomaly_periods = []
            fusion_periods = []

            for pred in predictions:
                # 检查各算法的预测结果
                algo_preds = pred.get('algorithm_predictions', {})

                # 前驱信号检测的预警时间段
                if 'earlysignal' in algo_preds and algo_preds['earlysignal'].get('predicted_label') == 1:
                    time_info = self._extract_time_from_filename(pred['filename'])
                    if time_info:
                        earlysignal_periods.append(time_info)

                # 异常检测的预警时间段（从详细数据中提取）
                if 'anomaly' in algo_preds and algo_preds['anomaly'].get('predicted_label') == 1:
                    anomaly_data_periods = algo_preds['anomaly'].get('anomaly_periods', [])
                    anomaly_periods.extend(anomaly_data_periods)

                # 融合结果预警（只有当融合预测为1时才添加）
                if pred['predicted_label'] == 1:
                    # 对于融合预警，我们使用异常检测的时间段（因为它更精确）
                    if 'anomaly' in algo_preds:
                        anomaly_data_periods = algo_preds['anomaly'].get('anomaly_periods', [])
                        fusion_periods.extend(anomaly_data_periods)
                    else:
                        # 如果没有异常检测数据，使用文件名提取的时间
                        time_info = self._extract_time_from_filename(pred['filename'])
                        if time_info:
                            fusion_periods.append(time_info)

            # 合并连续的时间段
            earlysignal_merged = self._merge_continuous_periods(earlysignal_periods)
            anomaly_merged = self._merge_continuous_periods(anomaly_periods)
            fusion_merged = self._merge_continuous_periods(fusion_periods)

            # 格式化预警时间段字符串
            earlysignal_str = self._format_warning_periods(earlysignal_merged)
            anomaly_str = self._format_warning_periods(anomaly_merged)
            fusion_str = self._format_warning_periods(fusion_merged)

            warning_periods.append({
                'well_name': well_name,
                'stuck_time': '不卡钻',  # 默认值，需要根据实际情况判断
                'earlysignal_warnings': earlysignal_str,
                'anomaly_warnings': anomaly_str,
                'fusion_warnings': fusion_str
            })

        logger.info(f"识别预警时间段完成，处理了 {len(well_groups)} 口井")
        return warning_periods

    def _extract_well_name_from_filename(self, filename: str) -> str:
        """从文件名提取井名，处理编码问题"""
        try:
            # 处理编码问题，尝试解码
            if isinstance(filename, bytes):
                filename = filename.decode('gbk', errors='ignore')

            # 常见的井名模式
            import re

            # 模式1: 直接包含井名（如：泸203H91-3）
            well_patterns = [
                r'([泸宁自阳足]\d+[A-Z]\d+-\d+)',  # 泸203H91-3, 宁209H53-6等
                r'(\d+井)',                        # 10井, 11井等
                r'([A-Z]+\d+)',                    # GS-H3ok等
                r'(ps\d+)',                        # ps101等
                r'([泸宁自阳足]\d+[A-Z]\d+)',       # 泸203H91等（没有-3的情况）
            ]

            for pattern in well_patterns:
                match = re.search(pattern, filename)
                if match:
                    return match.group(1)

            # 如果没有匹配到特定模式，尝试从文件名开头提取
            # 去除常见前缀
            clean_filename = filename
            prefixes_to_remove = ['earlysignal_', 'anomaly_results_']
            for prefix in prefixes_to_remove:
                if clean_filename.startswith(prefix):
                    clean_filename = clean_filename[len(prefix):]

            # 提取第一个下划线或点之前的部分作为井名
            if '_' in clean_filename:
                well_name = clean_filename.split('_')[0]
            elif '.' in clean_filename:
                well_name = clean_filename.split('.')[0]
            else:
                well_name = clean_filename

            # 如果井名为空或太短，使用默认值
            if not well_name or len(well_name) < 2:
                well_name = "未知井"

            return well_name

        except Exception as e:
            logger.warning(f"提取井名失败: {filename}, 错误: {e}")
            return "未知井"

    def _extract_time_from_filename(self, filename: str) -> Dict:
        """从文件名提取时间信息"""
        try:
            # 处理不同的文件名格式
            # 格式1: 井名_YYYY-MM-DD_HH-MM-SS_to_YYYY-MM-DD_HH-MM-SS.csv
            if '_to_' in filename:
                parts = filename.replace('.csv', '').split('_')
                if len(parts) >= 6:
                    start_date = parts[-5]
                    start_time = parts[-4].replace('-', ':')
                    end_date = parts[-2]
                    end_time = parts[-1].replace('-', ':')

                    return {
                        'start_time': f"{start_date} {start_time}",
                        'end_time': f"{end_date} {end_time}",
                        'filename': filename
                    }

            # 格式2: earlysignal_YYYY-MM-DD_HH-MM-SS_windowN.csv
            elif 'earlysignal_' in filename and '_window' in filename:
                parts = filename.replace('.csv', '').split('_')
                if len(parts) >= 4:
                    date_part = parts[1]  # YYYY-MM-DD
                    time_part = parts[2].replace('-', ':')  # HH:MM:SS

                    start_time = f"{date_part} {time_part}"
                    # 假设每个窗口是3分钟
                    from datetime import datetime, timedelta
                    start_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                    end_dt = start_dt + timedelta(minutes=3)
                    end_time = end_dt.strftime('%Y-%m-%d %H:%M:%S')

                    return {
                        'start_time': start_time,
                        'end_time': end_time,
                        'filename': filename
                    }

            # 格式3: 异常检测结果文件名（从时间戳数据中提取）
            # 这种情况下，我们需要从异常检测的详细数据中提取时间信息
            # 暂时返回None，让调用者处理

            return None

        except Exception as e:
            logger.warning(f"无法从文件名提取时间信息: {filename}, 错误: {e}")
            return None

    def _merge_continuous_periods(self, periods: List[Dict]) -> List[Dict]:
        """合并连续的时间段"""
        if not periods:
            return []

        # 按开始时间排序
        periods.sort(key=lambda x: x['start_time'])

        merged = []
        current_period = periods[0].copy()

        for period in periods[1:]:
            # 检查是否连续（这里简化处理，实际可能需要更复杂的逻辑）
            if self._is_continuous_period(current_period, period):
                # 合并时间段
                current_period['end_time'] = period['end_time']
            else:
                # 不连续，保存当前时间段，开始新的时间段
                merged.append(current_period)
                current_period = period.copy()

        # 添加最后一个时间段
        merged.append(current_period)

        return merged

    def _is_continuous_period(self, period1: Dict, period2: Dict) -> bool:
        """判断两个时间段是否连续"""
        try:
            from datetime import datetime, timedelta

            end1 = datetime.strptime(period1['end_time'], '%Y-%m-%d %H:%M:%S')
            start2 = datetime.strptime(period2['start_time'], '%Y-%m-%d %H:%M:%S')

            # 如果间隔小于等于3分钟，认为是连续的
            return (start2 - end1).total_seconds() <= 180

        except Exception:
            return False

    def _format_warning_periods(self, periods: List[Dict]) -> str:
        """格式化预警时间段字符串"""
        if not periods:
            return ""

        formatted_periods = []
        for period in periods:
            try:
                from datetime import datetime

                start = datetime.strptime(period['start_time'], '%Y-%m-%d %H:%M:%S')
                end = datetime.strptime(period['end_time'], '%Y-%m-%d %H:%M:%S')

                # 计算持续时间（分钟）
                if 'duration_minutes' in period:
                    duration = period['duration_minutes']
                else:
                    duration = int((end - start).total_seconds() / 60)

                # 格式化为 MM-DD HH:MM - HH:MM (持续X分钟)
                if start.date() == end.date():
                    # 同一天
                    formatted = f"{start.strftime('%m-%d %H:%M')} - {end.strftime('%H:%M')} (持续{duration}分钟)"
                else:
                    # 跨天
                    formatted = f"{start.strftime('%m-%d %H:%M')} - {end.strftime('%m-%d %H:%M')} (持续{duration}分钟)"

                formatted_periods.append(formatted)

            except Exception as e:
                logger.warning(f"格式化时间段失败: {period}, 错误: {e}")
                continue

        return "\n".join(formatted_periods)  # 使用换行符而不是"/n"

    def export_results(self, fusion_result: Dict[str, Any]) -> Dict[str, str]:
        """
        导出结果

        Args:
            fusion_result: 融合结果

        Returns:
            导出文件路径字典
        """
        logger.info("开始导出结果...")

        export_files = {}
        output_config = self.config['output']

        # 导出预警时间段
        if fusion_result.get('warning_periods'):
            warning_periods = fusion_result['warning_periods']

            # CSV格式
            if 'csv' in output_config['export_format']:
                csv_file = os.path.join(self.output_dir, 'warning_periods.csv')
                self._export_warning_periods_csv(warning_periods, csv_file)
                export_files['warning_periods_csv'] = csv_file

            # JSON格式
            if 'json' in output_config['export_format']:
                json_file = os.path.join(self.output_dir, 'warning_periods.json')
                self._export_warning_periods_json(warning_periods, json_file)
                export_files['warning_periods_json'] = json_file

        # 导出完整结果
        if output_config['save_final_results']:
            full_result_file = os.path.join(self.output_dir, 'full_results.json')
            with open(full_result_file, 'w', encoding='utf-8') as f:
                json.dump(fusion_result, f, ensure_ascii=False, indent=2, default=str)
            export_files['full_results'] = full_result_file

        # 导出执行日志
        log_file = os.path.join(self.output_dir, 'execution_log.txt')
        self._export_execution_log(fusion_result, log_file)
        export_files['execution_log'] = log_file

        logger.info(f"结果导出完成，文件保存在: {self.output_dir}")
        return export_files

    def _export_warning_periods_csv(self, warning_periods: List[Dict], filepath: str):
        """导出预警时间段到CSV"""
        if not warning_periods:
            # 创建空的CSV文件
            df = pd.DataFrame(columns=['井名', '卡钻时间', '算法1预警时间段', '算法2预警时间段', '融合预警时间段'])
        else:
            # 转换为所需格式
            formatted_data = []
            for period in warning_periods:
                formatted_data.append({
                    '井名': period['well_name'],
                    '卡钻时间': period['stuck_time'],
                    '算法1预警时间段': period['earlysignal_warnings'],
                    '算法2预警时间段': period['anomaly_warnings'],
                    '融合预警时间段': period['fusion_warnings']
                })
            df = pd.DataFrame(formatted_data)

        df.to_csv(filepath, index=False, encoding='utf-8-sig')  # 使用utf-8-sig支持中文
        logger.info(f"预警时间段已导出到CSV: {filepath}")

    def _export_warning_periods_json(self, warning_periods: List[Dict], filepath: str):
        """导出预警时间段到JSON"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(warning_periods, f, ensure_ascii=False, indent=2, default=str)
        logger.info(f"预警时间段已导出到JSON: {filepath}")

    def _export_execution_log(self, fusion_result: Dict[str, Any], filepath: str):
        """导出执行日志"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"算法融合执行报告\n")
            f.write(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"融合方法: {fusion_result.get('fusion_method', 'unknown')}\n")
            f.write(f"预警时间段数量: {len(fusion_result.get('warning_periods', []))}\n")
            f.write(f"执行状态: {fusion_result.get('status', 'unknown')}\n\n")

            # 各算法执行状态
            algorithm_results = fusion_result.get('algorithm_results', {})
            for algo_name, result in algorithm_results.items():
                f.write(f"{algo_name} 算法状态: {result.get('status', 'unknown')}\n")

        logger.info(f"执行日志已导出: {filepath}")

    def run_complete_pipeline(self, input_data_path: str) -> Dict[str, Any]:
        """
        运行完整的数据处理和算法融合流程

        Args:
            input_data_path: 输入数据文件路径

        Returns:
            完整的执行结果
        """
        logger.info("开始运行完整的算法融合流程...")

        try:
            # 1. 数据预处理
            logger.info("步骤1: 数据预处理")
            processed_files = self.preprocess_data(input_data_path)

            # 2. 运行算法
            logger.info("步骤2: 运行算法")
            algorithm_results = self.run_algorithms(processed_files)

            # 3. 结果融合
            logger.info("步骤3: 结果融合")
            fusion_result = self.fusion_results(algorithm_results)

            # 4. 导出结果
            logger.info("步骤4: 导出结果")
            export_files = self.export_results(fusion_result)

            # 添加导出文件信息到结果中
            fusion_result['export_files'] = export_files

            logger.info("完整流程执行成功!")
            return fusion_result

        except Exception as e:
            logger.error(f"流程执行失败: {e}")
            return {
                'status': 'pipeline_failed',
                'error': str(e),
                'warning_periods': []
            }


def create_default_config_file(config_path: str):
    """创建默认配置文件"""
    runner = IntegratedAlgorithmRunner()
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(runner.config, f, ensure_ascii=False, indent=2)
    print(f"默认配置文件已创建: {config_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='综合数据处理和算法融合运行脚本')

    parser.add_argument('input_data', type=str, nargs='?', help='输入测试数据文件路径')
    parser.add_argument('--config', type=str, default=None, help='配置文件路径')
    parser.add_argument('--output-dir', type=str, default='algorithm_results', help='输出目录')
    parser.add_argument('--create-config', type=str, default=None, help='创建默认配置文件')

    # 算法开关
    parser.add_argument('--disable-earlysignal', action='store_true', help='禁用前驱信号检测算法')
    parser.add_argument('--disable-anomaly', action='store_true', help='禁用异常检测算法')

    # 融合参数
    parser.add_argument('--fusion-method', type=str, default='weighted_average',
                       choices=['weighted_average', 'majority_voting'], help='融合方法')
    parser.add_argument('--fusion-threshold', type=float, default=0.7, help='融合阈值')
    parser.add_argument('--min-duration', type=int, default=3, help='最小预警持续时间（分钟）')

    args = parser.parse_args()

    # 创建默认配置文件
    if args.create_config:
        create_default_config_file(args.create_config)
        return 0

    # 检查输入文件
    if not args.input_data:
        print("错误: 需要提供输入数据文件路径")
        parser.print_help()
        return 1

    if not os.path.exists(args.input_data):
        print(f"错误: 输入数据文件不存在: {args.input_data}")
        return 1

    try:
        # 创建运行器
        runner = IntegratedAlgorithmRunner(args.config)

        # 更新配置
        runner.config['output']['base_dir'] = args.output_dir
        runner.output_dir = args.output_dir

        if args.disable_earlysignal:
            runner.config['earlysignal']['enabled'] = False
        if args.disable_anomaly:
            runner.config['anomaly']['enabled'] = False

        runner.config['fusion']['method'] = args.fusion_method
        runner.config['fusion']['thresholds']['fusion'] = args.fusion_threshold
        runner.config['fusion']['min_warning_duration'] = args.min_duration

        # 运行完整流程
        result = runner.run_complete_pipeline(args.input_data)

        # 输出结果摘要
        print("\n" + "="*80)
        print("执行结果摘要")
        print("="*80)
        print(f"执行状态: {result.get('status', 'unknown')}")
        print(f"融合方法: {result.get('fusion_method', 'unknown')}")
        print(f"预警时间段数量: {len(result.get('warning_periods', []))}")
        print(f"结果文件目录: {runner.output_dir}")

        if result.get('export_files'):
            print("\n导出文件:")
            for file_type, filepath in result['export_files'].items():
                print(f"  {file_type}: {filepath}")

        return 0 if result.get('status') == 'success' else 1

    except Exception as e:
        print(f"程序执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
